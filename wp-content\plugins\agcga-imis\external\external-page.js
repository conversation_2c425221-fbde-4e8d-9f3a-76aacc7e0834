/**
 * This JS file is intended to be loaded remotely from external iMIS site which will be loaded via iframe.
 *
 * This script handles:
 * - Common page scripting across external pages.
 * - Communication with the parent window using the postMessage API.
 */

window.pageInitialized = false;

const WP_SITE = "https://wp.agcga.org";

const ALLOW_DIRECT_ACCESS = true;

/**
 * Origin of the parent window for security validation
 * @type {string[]}
 */
const ALLOWED_PARENT_ORIGINS = [
	"https://www.agcga.org",
	"https://wp.agcga.org",
];

function isParentWindow() {
	return window.self === window.top;
}

function getUrlPartsAfterDomain() {
	return (
		window.location.pathname + window.location.search + window.location.hash
	);
}

/**
 * Checks if the origin is allowed
 *
 * @param {string} origin - The origin to check
 * @returns {boolean} - Whether the origin is allowed
 */
function isOriginAllowed(origin) {
	return ALLOWED_PARENT_ORIGINS.some(function (allowedOrigin) {
		return origin === allowedOrigin || origin.startsWith(allowedOrigin + ":");
	});
}

/**
 * Sends a message to the parent window
 *
 * @param {string} action - The action to perform
 * @param {Object} data - The data to send with the message
 * @returns {void}
 */
function sendMessageToParent(action, data) {
	if (data === undefined) {
		data = {};
	}

	if (isParentWindow()) {
		console.warn("Not running in an iframe");
		return;
	}

	const message = {
		source: "agcga-iframe",
		action: action,
		data: data,
	};

	// Send to parent window (target origin will be validated on the receiving end)
	window.parent.postMessage(message, "*");
}

/**
 * Handles messages received from the parent window
 *
 * @param {MessageEvent} event - The message event
 * @returns {void}
 */
function handleParentMessage(event) {
	// Security check: verify the origin
	if (!isOriginAllowed(event.origin)) {
		console.warn("Message from unauthorized origin: " + event.origin);
		return;
	}

	const messageData = event.data || {};
	const source = messageData.source;
	const action = messageData.action;

	/** @type Record<string, string|number> */
	const data = messageData.data;

	// Verify the message is from our parent script
	if (source !== "agcga-parent") {
		return;
	}

	console.log("Received message from parent: " + action, data);

	switch (action) {
		case "iframe-loaded":
			handleIframeLoaded(data);
			break;

		case "request-document-height":
			handleRequestDocumentHeight(data);
			break;

		case "navigate-history":
			handleNavigateHistory(data);
			break;

		default:
			console.log("Unhandled parent action: " + action);
	}
}

/**
 * Handle the "iframe-loaded" event from the parent window.
 *
 * @param {Record<string, string|number>} data The data sent with the message.
 */
function handleIframeLoaded(data) {
	//
}

/**
 * Handle the "request-document-height" action from the parent window.
 *
 * @param {Record<string, string|number>} data The data sent with the message.
 */
function handleRequestDocumentHeight(data) {
	setTimeout(() => {
		releaseContentPanelHeight();
		setTimeout(sendHeightToParent, 100);
	}, 500);
}

/**
 * Handle the "navigate-history" action from the parent window.
 *
 * @param {Record<string, string|number>} data The data sent with the message.
 */
function handleNavigateHistory(data) {
	const direction = data.direction;

	if (direction !== "forward" && direction !== "backward") {
		return;
	}

	if (direction === "forward") {
		history.forward();
	} else if (direction === "backward") {
		history.back();
	}
}

/**
 * The ".EmptyMasterContentPanel" element has a fixed height set by some JS (not from us).
 * That's why we need to remove that height.
 */
function releaseContentPanelHeight() {
	const panel = document.querySelector(".EmptyMasterContentPanel");
	if (!(panel instanceof HTMLElement)) return;
	panel.style.height = "auto";
}

/**
 * Gets the document height
 *
 * @returns {number} - The document height
 */
function getDocumentHeight() {
	return document.body.scrollHeight;

	// Get the height of the entire document
	// return Math.max(
	// 	document.body.scrollHeight,
	// 	document.documentElement.scrollHeight,
	// 	document.body.offsetHeight,
	// 	document.documentElement.offsetHeight,
	// 	document.body.clientHeight,
	// 	document.documentElement.clientHeight,
	// );
}

/**
 * Sends the current document height to the parent window
 */
function sendHeightToParent() {
	const height = getDocumentHeight();

	sendMessageToParent("resize-iframe", {
		height: height,
	});
}

function markIframeAsLoading() {
	sendMessageToParent("reload-iframe", {});
}

/**
 * Handles the load event of the window
 */
function handleWindowLoad() {
	sendMessageToParent("imis-page-loaded", {
		url: window.location.href,
		imisPath: getUrlPartsAfterDomain(),
	});

	setTimeout(() => {
		releaseContentPanelHeight();
		setTimeout(sendHeightToParent, 100);
	}, 500);
}

/**
 * Handles window resize events
 */
function handleWindowResize() {
	setTimeout(releaseContentPanelHeight, 500);
}

function handleWindowUnload() {
	markIframeAsLoading();
}

function setupResizeObserver() {
	const observer = new ResizeObserver(() => {
		const panel = document.querySelector(".EmptyMasterContentPanel");

		if (panel instanceof HTMLElement) {
			// Return early if the panel exist and its height is not yet set to "auto".
			if (panel.style.height !== "auto") {
				return;
			}
		}

		sendHeightToParent();
	});

	// Observe changes to the <body> or another container element
	observer.observe(document.body);
}

function init() {
	if (isParentWindow() && !ALLOW_DIRECT_ACCESS) {
		window.location.replace(
			`${WP_SITE}/imis/?imis-page=${getUrlPartsAfterDomain()}`,
		);
	}

	if (window.pageInitialized) return;
	window.pageInitialized = true;

	markIframeAsLoading();

	// Listen for messages from the parent window
	window.addEventListener("message", handleParentMessage);

	// Notify parent that iframe content has loaded
	window.addEventListener("load", handleWindowLoad);

	// Listen for window resize events
	window.addEventListener("resize", handleWindowResize);

	window.addEventListener("unload", handleWindowUnload);

	const memberDirectorySection = document.querySelector(
		".member-directory-section",
	);

	if (memberDirectorySection) setupResizeObserver();
}

init();
