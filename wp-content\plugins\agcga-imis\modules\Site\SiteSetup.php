<?php

declare( strict_types=1 );

namespace Mapsteps\Agcga\Module\Site;

use Mapsteps\Agcga\Module\Tables\ExecutiveListTable;
use Mapsteps\Agcga\Module\Tables\CalendarListTable;
use Mapsteps\Agcga\Module\Tables\CouncilMemberListTable;
use Mapsteps\Agcga\Module\Tables\DirectorListTable;
use Mapsteps\Agcga\Module\Tables\EducationCalendarListTable;
use Mapsteps\Agcga\Module\Tables\EventListTable;
use Mapsteps\Agcga\Module\Tables\ExecAllianceMemberListTable;
use Mapsteps\Agcga\Module\Tables\ExecutiveTeamListTable;
use Mapsteps\Agcga\Module\Tables\FoundationListTable;
use Mapsteps\Agcga\Module\Tables\GaSafetySIGListTable;
use Mapsteps\Agcga\Module\Tables\GovernorListTable;
use Mapsteps\Agcga\Module\Tables\LegislativeCommitteeListTable;
use Mapsteps\Agcga\Module\Tables\NetworkingOpportunitiesListTable;
use Mapsteps\Agcga\Module\Tables\PoliticalActionCommitteeListTable;
use Mapsteps\Agcga\Module\Tables\SafetyCommitteeListTable;
use Mapsteps\Agcga\Module\Tables\YLPMembersListTable;

class SiteSetup
{
	/**
	 * Slug of the pages that have an iMIS iframe.
	 *
	 * @var string[]
	 */
	private array $pages_with_imis_iframe = [
		'imis',
		'account',
		'member-directory',
		'event'
	];

	public function __construct()
	{
		add_action( 'admin_enqueue_scripts', [$this, 'enqueue_admin_assets'] );
		add_action( 'admin_menu', [ $this, 'agcga_imis_add_admin_menu' ] );

		add_action( 'wp_enqueue_scripts', [ $this, 'enqueue_site_assets' ] );
		add_filter( 'script_loader_tag', [$this, 'modify_module_script_attr'], 10, 3 );
		add_action( 'wp_footer', [$this, 'inject_iframe_loading_indicator'] );
	}

	public function enqueue_admin_assets()
	{
		wp_enqueue_style( 'agcga-admin', AGCGA_PLUGIN_URL . '/assets/css/admin.css', [], AGCGA_PLUGIN_VERSION );
	}

	private function current_page_has_imis_iframe(): bool
	{
		$condition = false;

		foreach ( $this->pages_with_imis_iframe as $page_slug ) {
		
			if ( is_page( $page_slug ) ) {
				$condition = true;
				break;
			}
		}

		return $condition;
	}

	public function enqueue_site_assets()
	{
		wp_enqueue_script( 'tailwindcss', 'https://cdn.tailwindcss.com', [], null, false );
		wp_enqueue_style( 'montserrat-font', 'https://fonts.googleapis.com/css2?family=Montserrat:wght@400;700&display=swap', [], null );
		wp_enqueue_style( 'agcga', AGCGA_PLUGIN_URL . '/assets/css/agcga.css', [], AGCGA_PLUGIN_VERSION, 'all' );

		wp_enqueue_script( 'dom-utils', AGCGA_PLUGIN_URL . '/assets/js/dom-utils.iife.js', [], AGCGA_PLUGIN_VERSION, [
			'in_footer' => false,
		] );

		wp_enqueue_script( 'agcga', AGCGA_PLUGIN_URL . '/assets/js/agcga.js', [], AGCGA_PLUGIN_VERSION, [
			'in_footer' => true,
			'strategy'  => 'defer',
		] );

		if ( $this->current_page_has_imis_iframe() ) {
			wp_enqueue_script( 'agcga-iframe', AGCGA_PLUGIN_URL . '/assets/js/agcga-iframe.js', [], AGCGA_PLUGIN_VERSION, [
				'in_footer' => true,
				'strategy'  => 'defer',
			] );
		}

		$agcga_global_js_object = 'const siteOpts = ' . wp_json_encode( [
			'siteUrl' => esc_url( site_url() ),
			'ajaxUrl' => admin_url( 'admin-ajax.php' ),
		] ) . ';';

		wp_add_inline_script( 'agcga', $agcga_global_js_object, 'before' );
	}

	public function inject_iframe_loading_indicator()
	{
		if ( !$this->current_page_has_imis_iframe() ) {
			return;
		}
		?>

		<div class="iframe-loading-overlay">
			<div class="loading-indicator"></div>
		</div>

		<?php
	}

	public function modify_module_script_attr( string $tag, string $handle, string $src ): string
	{
		if (
			'agcga' !== $handle
			&& 'agcga-iframe' !== $handle
			&& 'agcga-account' !== $handle
			&& 'agcga-member-directory' !== $handle
		) {
			return $tag;
		}

		return str_ireplace( ' src', ' type="module" src', $tag );
	}

	public function agcga_imis_add_admin_menu()
	{
		add_menu_page(
			'AGCGA IMIS',
			'AGCGA IMIS',
			'manage_options',
			'agcga-imis',
			[ $this, 'agcga_imis_admin_page' ],
			AGCGA_PLUGIN_URL . '/assets/images/agc-admin-menu.svg',
			20
		);

		add_submenu_page(
			'agcga-imis',
			'Upcoming Programs',
			'Upcoming Programs',
			'manage_options',
			'agcga-imis-upcoming-programs',
			[ $this, 'agcga_imis_upcoming_programs_page' ]
		);

		add_submenu_page(
			'agcga-imis',
			'Board of Directors',
			'Board of Directors',
			'manage_options',
			'agcga-imis-board-of-directors',
			[ $this, 'agcga_imis_board_of_directors_page' ]
		);

		add_submenu_page(
			'agcga-imis',
			'Board of Governors',
			'Board of Governors',
			'manage_options',
			'agcga-imis-board-of-governors',
			[ $this, 'agcga_imis_board_of_governors_page' ]
		);

		add_submenu_page(
			'agcga-imis',
			'Legislative Committee',
			'Legislative Committee',
			'manage_options',
			'agcga-imis-legislative-committee',
			[ $this, 'agcga_imis_legislative_committee_page' ]
		);

		add_submenu_page(
			'agcga-imis',
			'Safety Committee',
			'Safety Committee',
			'manage_options',
			'agcga-imis-safety-committee',
			[ $this, 'agcga_imis_safety_committee_page' ]
		);

		add_submenu_page(
			'agcga-imis',
			'Ga Safety SIG',
			'Ga Safety SIG',
			'manage_options',
			'agcga-imis-ga-safety-sig',
			[ $this, 'agcga_imis_ga_safety_sig_page' ]
		);

		add_submenu_page(
			'agcga-imis',
			'Political Action Committee',
			'Political Action Committee',
			'manage_options',
			'agcga-imis-political-action-committee',
			[ $this, 'agcga_imis_political_action_committee_page' ]
		);

		add_submenu_page(
			'agcga-imis',
			'Council Members',
			'Council Members',
			'manage_options',
			'agcga-imis-council-members',
			[ $this, 'agcga_imis_council_members_page' ]
		);

		add_submenu_page(
			'agcga-imis',
			'YLP Members',
			'YLP Members',
			'manage_options',
			'agcga-imis-ylp-members',
			[ $this, 'agcga_imis_ylp_members_page' ]
		);

		add_submenu_page(
			'agcga-imis',
			'Exec Alliance Members',
			'Exec Alliance Members',
			'manage_options',
			'agcga-imis-exec-alliance-members',
			[ $this, 'agcga_imis_exec_alliance_members_page' ]
		);

		add_submenu_page(
			'agcga-imis',
			'Executive Team',
			'Executive Team',
			'manage_options',
			'agcga-imis-executive-team',
			[ $this, 'agcga_imis_executive_team_page' ]
		);

		add_submenu_page(
			'agcga-imis',
			'Education Calendar',
			'Education Calendar',
			'manage_options',
			'agcga-imis-education-calendar',
			[ $this, 'agcga_imis_education_calendar_page' ]
		);

		add_submenu_page(
			'agcga-imis',
			'Networking Opportunities',
			'Networking Opportunities',
			'manage_options',
			'agcga-imis-networking-opportunities',
			[ $this, 'agcga_imis_networking_opportunities_page' ]
		);

		add_submenu_page(
			'agcga-imis',
			'Event Calendar',
			'Event Calendar',
			'manage_options',
			'agcga-imis-calendar',
			[ $this, 'agcga_imis_calendar_page' ]
		);

		add_submenu_page(
			'agcga-imis',
			'Board of Foundations',
			'Board of Foundations',
			'manage_options',
			'agcga-imis-board-of-foundations',
			[ $this, 'agcga_imis_board_of_foundations_page' ]
		);

		add_submenu_page(
			'agcga-imis',
			'Board of Executives',
			'Board of Executives',
			'manage_options',
			'agcga-imis-board-of-executives',
			[ $this, 'agcga_imis_board_of_executives_page' ]
		);
	}

	/**
	 * Admin page content.
	 */
	public function agcga_imis_admin_page() {
		$items = [
			'Board of Directors'         => 'board_of_directors',
			'Board of Governors'         => 'board_of_governors',
			'Legislative Committee'      => 'legislative_committee',
			'Safety Committee'           => 'safety_committee',
			'Ga Safety SIG'              => 'ga_safety_sig',
			'Political Action Committee' => 'political_action_committee',
			'Council Members'            => 'council_members',
			'YLP Members'                => 'ylp_members',
			'Exec Alliance Members'      => 'exec_alliance_members',
			'Executive Team'             => 'executive_team',
			'Education Calendar'         => 'education_calendar',
			'Event Calendar'             => 'event_calendar',
			'Networking Opportunities'   => 'networking_opportunities',
			'Board of Foundations'       => 'board_of_foundations',
			'Board of Executives'        => 'board_of_executives',
			'Presidents Council'         => 'presidents_council',
			'Operations Team'            => 'operations_team',
			'Staff Team'                 => 'staff_team',
			'Member Directory'           => 'member_directory',
		];
		?>

		<div class="wrap">
			<h1>Welcome to AGCGA IMIS</h1>
			<p>Below is a list of available shortcodes. Click the copy button to easily copy the shortcode.</p>
			<div style="display: flex; flex-wrap: wrap; gap: 15px;">

				<?php foreach ( $items as $name => $shortcode ) : ?>
					<div style="border: 1px solid #ddd; padding: 15px; border-radius: 8px; width: 250px; text-align: center; background: #f9f9f9; box-shadow: 2px 2px 5px rgba(0,0,0,0.1);">
						<p style="font-weight: bold; color: #0073aa; margin: 0;">
							<?=esc_html( $name )?>
						</p>
						<pre style="color: #333; font-size: 14px; background: #eee; padding: 5px; border-radius: 4px; border: 1px solid #ccc; display: inline-block;"><code>[<?=esc_html( $shortcode )?>]</code></pre>
						<button
							onclick="copyShortcode(this, '<?=esc_js( $shortcode )?>')"
							style="margin-top: 5px; padding: 5px 10px; border: none; background: #0073aa; color: #fff; border-radius: 4px; cursor: pointer;"
						>
							Copy
						</button>
						<span class="copy-confirmation" style="display: none; color: green; margin-left: 10px;">Copied!</span>
					</div>
				<?php endforeach; ?>

			</div>
		</div>

		<script>
		// JavaScript for copying the shortcode and showing a confirmation message.
		function copyShortcode(button, shortcode) {
			var text = "[" + shortcode + "]";
			var tempInput = document.createElement("input");
			tempInput.value = text;
			document.body.appendChild(tempInput);
			tempInput.select();
			document.execCommand("copy");
			document.body.removeChild(tempInput);

			// Show confirmation message
			var confirmation = button.nextElementSibling;
			confirmation.style.display = "inline";
			setTimeout(function() {
				confirmation.style.display = "none";
			}, 1500);
		}
		</script>

		<?php
	}

	public function agcga_imis_board_of_executives_page()
	{
		$list_table = new ExecutiveListTable();
		?>

		<div class="wrap">
			<h1><?php esc_html_e( 'Board of Executives', 'text-domain' ); ?></h1>

			<form method="get">
				<input type="hidden" name="page" value="board-of-executives-list">

			<?php
				$list_table->prepare_items();
				$list_table->search_box( 'Search', 'search_id' );
				$list_table->display();
			?>

			</form>

		</div>

		<?php
	}

	/**
	 * Upcoming Programs page content.
	 */
	public function agcga_imis_upcoming_programs_page()
	{
		$list_table = new EventListTable();
		?>

		<div class="wrap">
			<h1><?php esc_html_e( 'Upcoming Programs', 'text-domain' ); ?></h1>

			<form method="get">
				<input type="hidden" name="page" value="upcoming-programs-list">

			<?php
				$list_table->prepare_items();
				$list_table->search_box( 'Search', 'search_id' );
				$list_table->display();
			?>

			</form>

		</div>

		<?php
	}

	/**
	 * Board of directors page content.
	 */
	public function agcga_imis_board_of_directors_page()
	{
		$list_table = new DirectorListTable();
		?>

		<div class="wrap">
			<h1><?php esc_html_e( 'Board of Directors', 'text-domain' ); ?></h1>

			<form method="get">
				<input type="hidden" name="page" value="board-of-directors-list">

			<?php
				$list_table->prepare_items();
				$list_table->search_box( 'Search', 'search_id' );
				$list_table->display();
			?>

			</form>

		</div>

		<?php
	}

	/**
	 * Board of governors page content.
	 */
	public function agcga_imis_board_of_governors_page()
	{
		$list_table = new GovernorListTable();
		?>

		<div class="wrap">
			<h1><?php esc_html_e( 'Board of Governors', 'text-domain' ); ?></h1>

			<form method="get">
				<input type="hidden" name="page" value="board-of-governors-list">

			<?php
				$list_table->prepare_items();
				$list_table->search_box( 'Search', 'search_id' );
				$list_table->display();
			?>

			</form>

		</div>

		<?php
	}

	/**
	 * Legislative Committee page content.
	 */
	public function agcga_imis_legislative_committee_page()
	{
		$list_table = new LegislativeCommitteeListTable();
		?>

		<div class="wrap">
			<h1><?php esc_html_e( 'Legislative Committee', 'text-domain' ); ?></h1>

			<form method="get">
				<input type="hidden" name="page" value="legislative-committee-list">

			<?php
				$list_table->prepare_items();
				$list_table->search_box( 'Search', 'search_id' );
				$list_table->display();
			?>

			</form>

		</div>

		<?php
	}

	/**
	 * Safety Committee page content.
	 */
	public function agcga_imis_safety_committee_page()
	{
		$list_table = new SafetyCommitteeListTable();
		?>

		<div class="wrap">
			<h1><?php esc_html_e( 'Safety Committee', 'text-domain' ); ?></h1>

			<form method="get">
				<input type="hidden" name="page" value="safety-committee-list">

			<?php
				$list_table->prepare_items();
				$list_table->search_box( 'Search', 'search_id' );
				$list_table->display();
			?>

			</form>

		</div>

		<?php
	}

	/**
	 * Ga Safety SIG page content.
	 */
	public function agcga_imis_ga_safety_sig_page()
	{
		$list_table = new GaSafetySIGListTable();
		?>

		<div class="wrap">
			<h1><?php esc_html_e( 'Ga Safety SIG', 'text-domain' ); ?></h1>

			<form method="get">
				<input type="hidden" name="page" value="ga-safety-sig-list">

			<?php
				$list_table->prepare_items();
				$list_table->search_box( 'Search', 'search_id' );
				$list_table->display();
			?>

			</form>

		</div>

		<?php
	}

	/**
	 * Political Action Committee page content.
	 */
	public function agcga_imis_political_action_committee_page()
	{
		$list_table = new PoliticalActionCommitteeListTable();
		?>

		<div class="wrap">
			<h1><?php esc_html_e( 'Political Action Committee', 'text-domain' ); ?></h1>

			<form method="get">
				<input type="hidden" name="page" value="political-action-committee-list">

			<?php
				$list_table->prepare_items();
				$list_table->search_box( 'Search', 'search_id' );
				$list_table->display();
			?>

			</form>

		</div>

		<?php
	}

	/**
	 * Council Members page content.
	 */
	public function agcga_imis_council_members_page()
	{
		$list_table = new CouncilMemberListTable();
		?>

		<div class="wrap">
			<h1><?php esc_html_e( 'Council Members', 'text-domain' ); ?></h1>

			<form method="get">
				<input type="hidden" name="page" value="council-members-list">

			<?php
				$list_table->prepare_items();
				$list_table->search_box( 'Search', 'search_id' );
				$list_table->display();
			?>

			</form>

		</div>

		<?php
	}

	/**
	 * YLP Members page content.
	 */
	public function agcga_imis_ylp_members_page()
	{
		$list_table = new YLPMembersListTable();
		?>

		<div class="wrap">
			<h1><?php esc_html_e( 'YLP Members', 'text-domain' ); ?></h1>

			<form method="get">
				<input type="hidden" name="page" value="ylp-members-list">

			<?php
				$list_table->prepare_items();
				$list_table->search_box( 'Search', 'search_id' );
				$list_table->display();
			?>

			</form>

		</div>

		<?php
	}

	/**
	 * Exec Alliance Members page content.
	 */
	public function agcga_imis_exec_alliance_members_page()
	{
		$list_table = new ExecAllianceMemberListTable();
		?>

		<div class="wrap">
			<h1><?php esc_html_e( 'Exec Alliance Members', 'text-domain' ); ?></h1>

			<form method="get">
				<input type="hidden" name="page" value="exec-alliance-members-list">

			<?php
				$list_table->prepare_items();
				$list_table->search_box( 'Search', 'search_id' );
				$list_table->display();
			?>

			</form>

		</div>

		<?php
	}

	/**
	 * Executive Team page content.
	 */
	public function agcga_imis_executive_team_page()
	{
		$list_table = new ExecutiveTeamListTable();
		?>

		<div class="wrap">
			<h1><?php esc_html_e( 'Executive Team', 'text-domain' ); ?></h1>

			<form method="get">
				<input type="hidden" name="page" value="executive-team-list">

			<?php
				$list_table->prepare_items();
				$list_table->search_box( 'Search', 'search_id' );
				$list_table->display();
			?>

			</form>

		</div>

		<?php
	}

	/**
	 * Executive Team page content.
	 */
	public function agcga_imis_education_calendar_page()
	{
		$list_table = new EducationCalendarListTable();
		?>

		<div class="wrap">
			<h1><?php esc_html_e( 'Education Calendar', 'text-domain' ); ?></h1>

			<form method="get">
				<input type="hidden" name="page" value="education-calendar-list">

			<?php
				$list_table->prepare_items();
				$list_table->search_box( 'Search', 'search_id' );
				$list_table->display();
			?>

			</form>

		</div>

		<?php
	}

	/**
	 * Networking Opportunities page content.
	 */
	public function agcga_imis_networking_opportunities_page()
	{
		$list_table = new NetworkingOpportunitiesListTable();
		?>

		<div class="wrap">
			<h1><?php esc_html_e( 'Networking Opportunities', 'text-domain' ); ?></h1>

			<form method="get">
				<input type="hidden" name="page" value="networking-opportunities-list">

			<?php
				$list_table->prepare_items();
				$list_table->search_box( 'Search', 'search_id' );
				$list_table->display();
			?>

			</form>

		</div>

		<?php
	}

	/**
	 * Calendar page content.
	 */
	public function agcga_imis_calendar_page()
	{
		$list_table = new CalendarListTable();
		?>

		<div class="wrap">
			<h1><?php esc_html_e( 'Calendar', 'text-domain' ); ?></h1>

			<form method="get">
				<input type="hidden" name="page" value="calendar-list">

			<?php
				$list_table->prepare_items();
				$list_table->search_box( 'Search', 'search_id' );
				$list_table->display();
			?>

			</form>

		</div>

		<?php
	}

	/**
	 * Board of Foundations page content.
	 */
	public function agcga_imis_board_of_foundations_page()
	{
		$list_table = new FoundationListTable();
		?>

		<div class="wrap">
			<h1><?php esc_html_e( 'Board of Foundations', 'text-domain' ); ?></h1>

			<form method="get">
				<input type="hidden" name="page" value="board-of-foundations-list">

			<?php
				$list_table->prepare_items();
				$list_table->search_box( 'Search', 'search_id' );
				$list_table->display();
			?>

			</form>

		</div>

		<?php
	}
}
