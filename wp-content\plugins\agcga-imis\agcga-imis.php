<?php

declare( strict_types = 1 );

require_once __DIR__ . '/vendor/autoload.php';

use Mapsteps\Agcga\Module\Member\UserRole;
use Mapsteps\Agcga\Setup;

/**
 * Plugin Name: AGC Georgia - iMIS
 * Plugin URI: https://www.agcga.org/
 * Description: iMIS integration for AGC Georgia website
 * Version: 1.1.4
 * Author: Dwi Suryoko
 * Author URI: https://github.com/dwieyoko
 * License: GPL-3.0-or-later
 * License URI: https://www.gnu.org/licenses/gpl-3.0.html
 * Text Domain: agcga-imis
 * Domain Path: /languages
 * Requires at least: 6.7
 * Requires PHP: 8.2
 *
 * @package agcga
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

$dotenv = \Dotenv\Dotenv::createImmutable( ABSPATH );
$dotenv->safeLoad();

define( 'AGCGA_PLUGIN_DIR', rtrim( plugin_dir_path( __FILE__ ), '/' ) );
define( 'AGCGA_PLUGIN_URL', rtrim( plugin_dir_url( __FILE__ ), '/' ) );
define( 'AGCGA_PLUGIN_VERSION', '1.1.4' );
define( 'LEGACYHOME_PLUGIN_FILE', 'agcga-imis/agcga-imis.php' );

function agcga_imis_activation() {
	( new UserRole() )->addImisRole();
	flush_rewrite_rules( false );
}
register_activation_hook( __FILE__, 'agcga_imis_activation' );

function agcga_imis_uninstall() {
	( new UserRole() )->removeImisRole();
}
register_uninstall_hook( __FILE__, 'agcga_imis_uninstall' );

new Setup();
