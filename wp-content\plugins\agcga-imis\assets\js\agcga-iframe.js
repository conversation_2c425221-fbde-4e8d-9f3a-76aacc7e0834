/**
 * Script for "Account" page (single iframe).
 *
 * This script handles communication between the main window and the iframe
 * using the postMessage API.
 */

import { findHtmlEl } from "./dom-utils.js";

const iframeEl = findHtmlEl(".agcga-iframe");
const loadingOverlay = findHtmlEl(".iframe-loading-overlay");

/**
 * Origins of the iframe content for security validation
 * @type {string[]}
 */
let ALLOWED_ORIGINS = [
	"https://www.agcga.org",
	"https://wp.agcga.org",
	"https://imis.agcga.org",
];

/** @type {string|undefined} */
let currentImisPath = undefined;

let lastHistoryIndex = 0;

/**
 * Sends a message to the iframe
 *
 * @param {string} action - The action to perform
 * @param {Object} data - The data to send with the message
 * @returns {void}
 */
function sendMessageToIframe(action, data) {
	if (data === undefined) {
		data = {};
	}

	if (
		!iframeEl ||
		!(iframeEl instanceof HTMLIFrameElement) ||
		!iframeEl.contentWindow
	) {
		console.warn("Iframe not found or not accessible");
		return;
	}

	let message = {
		source: "agcga-parent",
		action: action,
		data: data,
	};

	// Use * as targetOrigin since we're sending to our own iframe
	// Security will be handled on the receiving end
	iframeEl.contentWindow.postMessage(message, "*");
}

/**
 * Checks if the origin is allowed
 *
 * @param {string} origin - The origin to check
 * @returns {boolean} - Whether the origin is allowed
 */
function isOriginAllowed(origin) {
	return ALLOWED_ORIGINS.some(function (allowedOrigin) {
		return origin === allowedOrigin || origin.startsWith(allowedOrigin + ":");
	});
}

/**
 * Handles messages received from the iframe
 *
 * @param {MessageEvent} event - The message event
 * @returns {void}
 */
function handleIframeMessage(event) {
	// Security check: verify the origin
	if (!isOriginAllowed(event.origin)) {
		console.warn("Message from unauthorized origin: " + event.origin);
		return;
	}

	const messageData = event.data || {};

	const source = messageData.source;
	const action = messageData.action;

	/** @type Record<string, string|number> */
	const data = messageData.data;

	// Verify the message is from our iframe script
	if (source !== "agcga-iframe") {
		return;
	}

	// Handle different actions from the iframe
	switch (action) {
		case "reload-iframe":
			handleReloadIframeEvent();
			break;

		case "imis-page-loaded":
			handleImisPageLoadedEvent(data);
			break;

		case "login-success":
			console.log("User logged in successfully in iMIS");
			// You can add code here to update the parent page if needed
			break;

		case "logout":
			console.log("User logged out from iMIS");
			// You can add code here to handle logout in the parent page if needed
			break;

		case "resize-iframe":
			if (data.height && iframeEl) {
				setIframeHeight(Number(data.height));
			}
			break;

		default:
			console.log("Unhandled iframe action: " + action);
	}
}

function handleReloadIframeEvent() {
	startIframeLoading();
}

/**
 * Build URL with search params.
 *
 * @param {string} url The URL to modify
 * @param {Record<string, string>} [searchParams] URL search params object.
 *
 * @returns {string}
 */
function buildURL(url, searchParams) {
	const urlObj = new URL(url);

	if (searchParams) {
		Object.entries(searchParams).forEach(([key, value]) => {
			urlObj.searchParams.set(key, value);
		});
	}

	return urlObj.toString();
}

/**
 * Handles the "imis-page-loaded" event from inside the iframe.
 *
 * @param {Record<string, string|number>} data The data sent with the message.
 */
function handleImisPageLoadedEvent(data) {
	const imisPath = data.imisPath ? String(data.imisPath) : undefined;

	console.log(
		`iMIS page loaded: carried path: ${imisPath}, current path: ${currentImisPath}`,
	);

	if (currentImisPath !== imisPath) {
		if (currentImisPath && imisPath) {
			const url = buildURL(window.location.href, {
				"imis-page": imisPath,
			});

			const pushStateObj = {
				imisPath: imisPath,
				index: window.history.state?.index + 1 || 0,
			};

			console.log("Pushing state:", pushStateObj);

			window.history.pushState(pushStateObj, "", url);
		} else {
			window.history.replaceState(
				{
					imisPath: imisPath,
					index: window.history.state?.index || 0,
				},
				"",
				window.location.href,
			);
		}

		currentImisPath = imisPath;
		lastHistoryIndex = window.history.state?.index || 0;
	}

	stopIframeLoading();
}

/**
 * Determines the direction of navigation based on the history index.
 *
 * @param {PopStateEvent} event
 * @returns {"forward"|"backward"|"none"} - The direction of navigation
 */
function getNavigationDirection(event) {
	const currentIndex = event.state?.index || 0;
	const direction = currentIndex - lastHistoryIndex;
	lastHistoryIndex = currentIndex;

	return direction > 0 ? "forward" : direction < 0 ? "backward" : "none";
}

/**
 * Visually hides the loading state of the iframe
 */
function stopIframeLoading() {
	if (!loadingOverlay) return;
	loadingOverlay.classList.remove("is-loading");
}

/**
 * Visually shows the loading state of the iframe
 */
function startIframeLoading() {
	if (!loadingOverlay) return;
	loadingOverlay.classList.add("is-loading");
}

function requestIframeHeight() {
	sendMessageToIframe("request-document-height", {});
}

/**
 * Set the iframe height based on content.
 *
 * @param {number} height - The height to set for the iframe.
 */
function setIframeHeight(height) {
	if (!iframeEl) return;

	// Add a small buffer to prevent scrollbars (optional)
	let adjustedHeight = height + 20;

	// Set the iframe height
	iframeEl.style.height = adjustedHeight + "px";

	// Log the adjustment
	console.log("Adjusted iframe height to: " + adjustedHeight + "px");

	stopIframeLoading();
}

/**
 * Handles the load event of the iframe
 */
function handleIframeLoad() {
	sendMessageToIframe("iframe-loaded", {
		parentUrl: window.location.href,
	});
}

/**
 * Handles the popstate event
 *
 * @param {PopStateEvent} event
 */
function handlePopState(event) {
	const direction = getNavigationDirection(event);

	console.log("History navigation: " + direction);

	if (direction === "none") return;

	sendMessageToIframe("navigate-history", {
		direction: direction,
	});
}

function init() {
	// Set the page title in the hero section if it exists
	const headerHero = findHtmlEl("#uicore-tb-pagetitle");
	headerHero?.remove();

	window.addEventListener("load", requestIframeHeight);
	window.addEventListener("resize", requestIframeHeight);
	window.addEventListener("popstate", handlePopState);

	// Initialize iframe communication
	if (iframeEl) {
		// Listen for messages from the iframe
		window.addEventListener("message", handleIframeMessage);

		// When iframe loads, send an initialization message
		iframeEl.addEventListener("load", handleIframeLoad);
	} else {
		console.warn("Iframe element not found");
	}
}

init();
