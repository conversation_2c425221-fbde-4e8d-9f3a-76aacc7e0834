import pandas as pd
from datetime import datetime

try:
    # Harga per item (berdasarkan file sebelumnya)
    harga_meses = 2500
    harga_sosis = 3000
    harga_keju = 2500  # Dikoreksi dari 3000 ke 2500
    harga_piscok = 3000  # Roti pisang coklat
    harga_abon = 2500  # Dikoreksi dari 3000 ke 2500
    harga_selai = 2500  # Dikoreksi dari 3000 ke 2500
    harga_kacang_hijau = 3000
    harga_kacang_merah = 3000

    # Data pesanan berdasarkan percakapan WhatsApp 5/14/25 - 5/29/25
    pesanan_mei_2025 = [
        # 14 Mei 2025 (5/14/25, 17:48)
        ['14 Mei 2025', 'Roti keju', 40, harga_keju, 40 * harga_keju],

        # 21 Mei 2025 (5/20/25, 10:02 - untuk besok subuh)
        ['21 Mei 2025', 'Roti meses', 105, harga_meses, 105 * harga_meses],

        # 22 Mei 2025 (5/21/25, 05:31 - Revisi untuk Kamis 22 mei 25)
        ['22 Mei 2025', 'Roti keju', 105, harga_keju, 105 * harga_keju],
        ['22 Mei 2025', 'Roti piscok', 40, harga_piscok, 40 * harga_piscok],
        ['22 <PERSON> 2025', 'Roti abon', 2, harga_abon, 2 * harga_abon],
        ['22 Mei 2025', 'Roti meses', 2, harga_meses, 2 * harga_meses],
        ['22 Mei 2025', 'Roti sosis', 2, harga_sosis, 2 * harga_sosis],
        ['22 Mei 2025', 'Roti piscok', 140, harga_piscok, 140 * harga_piscok],  # terpisah
        ['22 Mei 2025', 'Roti abon', 140, harga_abon, 140 * harga_abon],  # terpisah

        # 22 Mei 2025 (5/22/25, 08:31 - malam)
        ['22 Mei 2025', 'Roti piscok', 52, harga_piscok, 52 * harga_piscok],

        # 22 Mei 2025 (5/22/25, 19:35 - tambahan)
        ['22 Mei 2025', 'Roti meses', 5, harga_meses, 5 * harga_meses],
        ['22 Mei 2025', 'Roti piscok', 4, harga_piscok, 4 * harga_piscok],

        # 26 Mei 2025 (5/25/25, 09:26 - untuk besok subuh)
        ['26 Mei 2025', 'Roti selai', 55, harga_selai, 55 * harga_selai],

        # 27 Mei 2025 (5/26/25, 10:01 - untuk besok subuh)
        ['27 Mei 2025', 'Roti kacang hijau', 45, harga_kacang_hijau, 45 * harga_kacang_hijau],
        ['27 Mei 2025', 'Roti kacang merah', 63, harga_kacang_merah, 63 * harga_kacang_merah],

        # 27 Mei 2025 (5/26/25, 22:14 - tambahan 5 meses)
        ['27 Mei 2025', 'Roti meses', 5, harga_meses, 5 * harga_meses],
    ]

    # Buat DataFrame
    df = pd.DataFrame(pesanan_mei_2025, columns=['Tanggal', 'Nama Roti', 'Jumlah', 'Harga', 'Total'])

    # Hitung grand total
    grand_total = df['Total'].sum()

    # Tambahkan baris GRAND TOTAL
    grand_total_row = pd.DataFrame({
        'Tanggal': ['GRAND TOTAL'],
        'Nama Roti': [None],
        'Jumlah': [None],
        'Harga': [None],
        'Total': [grand_total]
    })

    df_final = pd.concat([df, grand_total_row], ignore_index=True)

    # Simpan ke file Excel
    output_file = "Pemesanan_Roti_Mei_2025.xlsx"
    df_final.to_excel(output_file, index=False)

    print(f"✅ File berhasil dibuat: {output_file}")
    print(f"📊 Total baris: {len(df_final)}")

    # Tampilkan ringkasan
    print(f"\n📅 RINGKASAN PESANAN MEI 2025:")
    print("=" * 50)

    # Ringkasan per tanggal
    df_data = df  # Tanpa GRAND TOTAL
    tanggal_summary = df_data.groupby('Tanggal').agg({
        'Jumlah': 'sum',
        'Total': 'sum'
    })

    for tanggal, data in tanggal_summary.iterrows():
        qty = int(data['Jumlah'])
        total = int(data['Total'])
        print(f"📅 {tanggal}: {qty:3d} pcs - Rp {total:,}")

    print("\n" + "=" * 50)

    # Ringkasan per jenis roti
    print(f"\n🍞 RINGKASAN PER JENIS ROTI:")
    print("-" * 40)

    item_summary = df_data.groupby('Nama Roti').agg({
        'Jumlah': 'sum',
        'Total': 'sum'
    }).sort_values('Total', ascending=False)

    for item, data in item_summary.iterrows():
        qty = int(data['Jumlah'])
        total = int(data['Total'])
        print(f"🥖 {item:<20} : {qty:4d} pcs - Rp {total:,}")

    print("\n" + "=" * 50)

    # Total keseluruhan
    total_qty = df_data['Jumlah'].sum()
    total_revenue = df_data['Total'].sum()

    print(f"\n💰 TOTAL KESELURUHAN:")
    print(f"📦 Total Kuantitas  : {int(total_qty):,} pcs")
    print(f"💵 Total Pendapatan : Rp {int(total_revenue):,}")
    print(f"📊 Total Pesanan    : {len(df_data)} baris pesanan")
    print(f"📅 Periode          : 14-27 Mei 2025")

    print(f"\n📋 DETAIL PESANAN:")
    print("-" * 60)
    for i, row in df_data.iterrows():
        print(f"{i+1:2d}. {row['Tanggal']}: {row['Jumlah']:3d} {row['Nama Roti']} - Rp {row['Total']:,}")

except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
